<div id="toast-container" class="guest-toast-container">
    <?php $__currentLoopData = ['success' => '✓', 'error' => '❌', 'info' => 'ℹ️']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type => $icon): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <?php if(session($type)): ?>
            <?php
                $toastClass = [
                    'success' => 'guest-toast-success',
                    'error' => 'guest-toast-error',
                    'info' => 'guest-toast-info',
                ][$type];
            ?>

            <div class="guest-toast <?php echo e($toastClass); ?>" data-type="<?php echo e($type); ?>">
                <!-- Icon -->
                <div class="guest-toast-icon">
                    <?php echo e($icon); ?>

                </div>

                <!-- Message -->
                <div class="guest-toast-message">
                    <?php echo e(session($type)); ?>

                </div>

                <!-- Close Button -->
                <button class="guest-toast-close" onclick="closeToast(this)">
                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor" stroke-width="2">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
        <?php endif; ?>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
</div>

<style>
.guest-toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    max-width: 400px;
    width: 100%;
}

.guest-toast {
    display: flex;
    align-items: flex-start;
    padding: 16px;
    margin-bottom: 12px;
    background: rgba(0, 0, 0, 0.9);
    color: white;
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
    border-left: 4px solid;
    backdrop-filter: blur(10px);
    opacity: 0;
    transform: translateX(100%) scale(0.95);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.guest-toast.show {
    opacity: 1;
    transform: translateX(0) scale(1);
}

.guest-toast.hide {
    opacity: 0;
    transform: translateX(100%) scale(0.95);
}

.guest-toast-success {
    border-left-color: #10b981;
}

.guest-toast-error {
    border-left-color: #ef4444;
}

.guest-toast-info {
    border-left-color: #3b82f6;
}

.guest-toast-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    margin-right: 12px;
    font-size: 18px;
    border-radius: 6px;
    flex-shrink: 0;
}

.guest-toast-success .guest-toast-icon {
    background-color: #10b981;
}

.guest-toast-error .guest-toast-icon {
    background-color: #ef4444;
}

.guest-toast-info .guest-toast-icon {
    background-color: #3b82f6;
}

.guest-toast-message {
    flex: 1;
    font-size: 14px;
    font-weight: 500;
    line-height: 1.4;
    padding-top: 4px;
}

.guest-toast-close {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    cursor: pointer;
    padding: 4px;
    margin-left: 12px;
    border-radius: 4px;
    transition: color 0.2s ease;
    flex-shrink: 0;
}

.guest-toast-close:hover {
    color: white;
    background-color: rgba(255, 255, 255, 0.1);
}

@media (max-width: 768px) {
    .guest-toast-container {
        top: 10px;
        right: 10px;
        left: 10px;
        max-width: none;
    }

    .guest-toast {
        padding: 12px;
        font-size: 13px;
    }

    .guest-toast-icon {
        width: 28px;
        height: 28px;
        font-size: 16px;
    }
}
</style>

<script>
// Guest Toast functionality
function closeToast(button) {
    const toast = button.closest('.guest-toast');
    toast.classList.add('hide');
    setTimeout(() => {
        toast.remove();
    }, 400);
}

// Show toasts on page load
document.addEventListener('DOMContentLoaded', function() {
    const toasts = document.querySelectorAll('.guest-toast');

    toasts.forEach((toast, index) => {
        // Stagger the appearance of multiple toasts
        setTimeout(() => {
            toast.classList.add('show');

            // Auto-hide after 5 seconds
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.classList.add('hide');
                    setTimeout(() => {
                        if (toast.parentNode) {
                            toast.remove();
                        }
                    }, 400);
                }
            }, 5000);
        }, index * 200); // 200ms delay between each toast
    });
});

// Function to create dynamic toasts (for JavaScript usage)
function showGuestToast(message, type = 'info') {
    const container = document.getElementById('toast-container');
    if (!container) return;

    const icons = {
        success: '✓',
        error: '❌',
        info: 'ℹ️'
    };

    const toast = document.createElement('div');
    toast.className = `guest-toast guest-toast-${type}`;
    toast.innerHTML = `
        <div class="guest-toast-icon">${icons[type] || icons.info}</div>
        <div class="guest-toast-message">${message}</div>
        <button class="guest-toast-close" onclick="closeToast(this)">
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="none" viewBox="0 0 24 24"
                stroke="currentColor" stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
            </svg>
        </button>
    `;

    container.appendChild(toast);

    // Show toast
    setTimeout(() => {
        toast.classList.add('show');
    }, 100);

    // Auto-hide after 5 seconds
    setTimeout(() => {
        if (toast.parentNode) {
            toast.classList.add('hide');
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.remove();
                }
            }, 400);
        }
    }, 5000);
}
</script>
<?php /**PATH C:\xampp\htdocs\trash\resources\views/components/guest-toast.blade.php ENDPATH**/ ?>