<div id="toast-container" class="guest-toast-container">
    @foreach (['success' => '✓', 'error' => '❌', 'info' => 'ℹ️'] as $type => $icon)
        @if (session($type))
            @php
                $toastClass = [
                    'success' => 'guest-toast-success',
                    'error' => 'guest-toast-error',
                    'info' => 'guest-toast-info',
                ][$type];
            @endphp

            @php
                $iconType = in_array($type, ['success', 'toast_success']) ? 'success' :
                           (in_array($type, ['error', 'toast_error']) ? 'error' : 'info');

                $titles = [
                    'success' => 'Success!',
                    'error' => 'Error!',
                    'info' => 'Information'
                ];
            @endphp

            <div class="guest-toast {{ $toastClass }}" data-type="{{ $type }}">
                <!-- Icon -->
                <div class="guest-toast-icon">
                    @if($iconType === 'success')
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    @elseif($iconType === 'error')
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z" />
                        </svg>
                    @else
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    @endif
                </div>

                <!-- Content -->
                <div class="guest-toast-content">
                    <h4 class="guest-toast-title">{{ $titles[$iconType] }}</h4>
                    <p class="guest-toast-message">{{ session($type) }}</p>
                </div>

                <!-- Close Button -->
                <button class="guest-toast-close" onclick="closeToast(this)" aria-label="Close notification">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
        @endif
    @endforeach
</div>

<style>
.guest-toast-container {
    position: fixed;
    top: 32px;
    right: 32px;
    z-index: 10000;
    max-width: 380px;
    width: 100%;
    pointer-events: none;
}

.guest-toast {
    display: flex;
    align-items: flex-start;
    padding: 20px 24px;
    margin-bottom: 20px;
    background: #ffffff;
    color: #1f2937;
    border-radius: 20px;
    box-shadow:
        0 25px 50px -12px rgba(0, 0, 0, 0.25),
        0 0 0 1px rgba(0, 0, 0, 0.05);
    border: none;
    opacity: 0;
    transform: translateX(120%) rotateY(15deg);
    transition: all 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
    position: relative;
    pointer-events: auto;
    overflow: hidden;
}

.guest-toast::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

.guest-toast.show {
    opacity: 1;
    transform: translateX(0) rotateY(0deg);
}

.guest-toast.hide {
    opacity: 0;
    transform: translateX(120%) rotateY(-15deg) scale(0.8);
    transition: all 0.4s cubic-bezier(0.55, 0.085, 0.68, 0.53);
}

.guest-toast-success {
    background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
    color: #166534;
    box-shadow:
        0 25px 50px -12px rgba(34, 197, 94, 0.25),
        0 0 0 1px rgba(34, 197, 94, 0.1);
}

.guest-toast-error {
    background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
    color: #991b1b;
    box-shadow:
        0 25px 50px -12px rgba(239, 68, 68, 0.25),
        0 0 0 1px rgba(239, 68, 68, 0.1);
}

.guest-toast-info {
    background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
    color: #1e40af;
    box-shadow:
        0 25px 50px -12px rgba(59, 130, 246, 0.25),
        0 0 0 1px rgba(59, 130, 246, 0.1);
}

.guest-toast-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    margin-right: 16px;
    border-radius: 12px;
    flex-shrink: 0;
    position: relative;
    overflow: hidden;
}

.guest-toast-success .guest-toast-icon {
    background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
    color: #059669;
}

.guest-toast-error .guest-toast-icon {
    background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
    color: #dc2626;
}

.guest-toast-info .guest-toast-icon {
    background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
    color: #2563eb;
}

.guest-toast-icon svg {
    width: 20px;
    height: 20px;
    stroke-width: 2.5;
}

.guest-toast-message {
    flex: 1;
    font-size: 15px;
    line-height: 1.5;
    font-weight: 500;
    letter-spacing: -0.01em;
}

.guest-toast-close {
    background: none;
    border: none;
    color: #9ca3af;
    cursor: pointer;
    padding: 8px;
    border-radius: 8px;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    margin-left: 12px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.guest-toast-close:hover {
    color: #6b7280;
    background: rgba(0, 0, 0, 0.05);
    transform: scale(1.1);
}

.guest-toast-close svg {
    width: 16px;
    height: 16px;
    stroke-width: 2;
}

@media (max-width: 768px) {
    .guest-toast-container {
        top: 16px;
        right: 16px;
        left: 16px;
        max-width: none;
    }

    .guest-toast {
        padding: 16px 18px;
        margin-bottom: 12px;
    }

    .guest-toast-icon {
        width: 36px;
        height: 36px;
        margin-right: 14px;
    }

    .guest-toast-icon svg {
        width: 18px;
        height: 18px;
    }

    .guest-toast-message {
        font-size: 14px;
    }
}

@media (prefers-reduced-motion: reduce) {
    .guest-toast {
        transition: opacity 0.3s ease;
        transform: none;
    }

    .guest-toast.show {
        transform: none;
    }

    .guest-toast.hide {
        transform: none;
    }
}
</style>

<script>
// Guest Toast functionality
function closeToast(button) {
    const toast = button.closest('.guest-toast');
    toast.classList.add('hide');
    setTimeout(() => {
        toast.remove();
    }, 400);
}

// Show toasts on page load
document.addEventListener('DOMContentLoaded', function() {
    const toasts = document.querySelectorAll('.guest-toast');

    toasts.forEach((toast, index) => {
        // Stagger the appearance of multiple toasts
        setTimeout(() => {
            toast.classList.add('show');

            // Auto-hide after 5 seconds
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.classList.add('hide');
                    setTimeout(() => {
                        if (toast.parentNode) {
                            toast.remove();
                        }
                    }, 400);
                }
            }, 5000);
        }, index * 200); // 200ms delay between each toast
    });
});

// Function to create dynamic toasts (for JavaScript usage)
function showGuestToast(message, type = 'info') {
    const container = document.getElementById('toast-container');
    if (!container) return;

    const icons = {
        success: '✓',
        error: '❌',
        info: 'ℹ️'
    };

    const toast = document.createElement('div');
    toast.className = `guest-toast guest-toast-${type}`;
    toast.innerHTML = `
        <div class="guest-toast-icon">${icons[type] || icons.info}</div>
        <div class="guest-toast-message">${message}</div>
        <button class="guest-toast-close" onclick="closeToast(this)">
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="none" viewBox="0 0 24 24"
                stroke="currentColor" stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
            </svg>
        </button>
    `;

    container.appendChild(toast);

    // Show toast
    setTimeout(() => {
        toast.classList.add('show');
    }, 100);

    // Auto-hide after 5 seconds
    setTimeout(() => {
        if (toast.parentNode) {
            toast.classList.add('hide');
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.remove();
                }
            }, 400);
        }
    }, 5000);
}
</script>
