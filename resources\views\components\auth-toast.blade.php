<style>
    .auth-toast-container {
        position: fixed;
        top: 24px;
        right: 24px;
        z-index: 9999;
        max-width: 420px;
        width: 100%;
        pointer-events: none;
    }

    .auth-toast {
        display: flex;
        align-items: center;
        padding: 18px 20px;
        margin-bottom: 16px;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%);
        color: #374151;
        border-radius: 16px;
        box-shadow:
            0 20px 25px -5px rgba(0, 0, 0, 0.1),
            0 10px 10px -5px rgba(0, 0, 0, 0.04),
            0 0 0 1px rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(20px) saturate(180%);
        -webkit-backdrop-filter: blur(20px) saturate(180%);
        opacity: 0;
        transform: translateX(100%) translateY(-10px) scale(0.95);
        transition: all 0.5s cubic-bezier(0.16, 1, 0.3, 1);
        position: relative;
        pointer-events: auto;
        overflow: hidden;
    }

    .auth-toast::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, transparent, currentColor, transparent);
        opacity: 0.6;
    }

    .auth-toast.show {
        opacity: 1;
        transform: translateX(0) translateY(0) scale(1);
    }

    .auth-toast.hide {
        opacity: 0;
        transform: translateX(100%) translateY(-10px) scale(0.95);
        transition: all 0.3s cubic-bezier(0.4, 0, 1, 1);
    }

    .auth-toast-success {
        color: #059669;
        border-left: 4px solid #10b981;
    }

    .auth-toast-success::before {
        background: linear-gradient(90deg, transparent, #10b981, transparent);
    }

    .auth-toast-error {
        color: #dc2626;
        border-left: 4px solid #ef4444;
    }

    .auth-toast-error::before {
        background: linear-gradient(90deg, transparent, #ef4444, transparent);
    }

    .auth-toast-info {
        color: #2563eb;
        border-left: 4px solid #3b82f6;
    }

    .auth-toast-info::before {
        background: linear-gradient(90deg, transparent, #3b82f6, transparent);
    }

    .auth-toast-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        margin-right: 16px;
        border-radius: 12px;
        flex-shrink: 0;
        position: relative;
        overflow: hidden;
    }

    .auth-toast-success .auth-toast-icon {
        background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
        color: #059669;
    }

    .auth-toast-error .auth-toast-icon {
        background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
        color: #dc2626;
    }

    .auth-toast-info .auth-toast-icon {
        background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
        color: #2563eb;
    }

    .auth-toast-icon svg {
        width: 20px;
        height: 20px;
        stroke-width: 2.5;
    }

    .auth-toast-message {
        flex: 1;
        font-size: 15px;
        line-height: 1.5;
        font-weight: 500;
        letter-spacing: -0.01em;
    }

    .auth-toast-close {
        background: none;
        border: none;
        color: #9ca3af;
        cursor: pointer;
        padding: 8px;
        border-radius: 8px;
        transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        margin-left: 12px;
        flex-shrink: 0;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .auth-toast-close:hover {
        color: #6b7280;
        background: rgba(0, 0, 0, 0.05);
        transform: scale(1.1);
    }

    .auth-toast-close svg {
        width: 16px;
        height: 16px;
        stroke-width: 2;
    }

    @media (max-width: 768px) {
        .auth-toast-container {
            top: 16px;
            right: 16px;
            left: 16px;
            max-width: none;
        }

        .auth-toast {
            padding: 16px 18px;
            margin-bottom: 12px;
        }

        .auth-toast-icon {
            width: 36px;
            height: 36px;
            margin-right: 14px;
        }

        .auth-toast-icon svg {
            width: 18px;
            height: 18px;
        }

        .auth-toast-message {
            font-size: 14px;
        }
    }

    @media (prefers-reduced-motion: reduce) {
        .auth-toast {
            transition: opacity 0.3s ease;
            transform: none;
        }

        .auth-toast.show {
            transform: none;
        }

        .auth-toast.hide {
            transform: none;
        }
    }

</style>

<!-- Modern Auth Toast Container -->
<div id="auth-toast-container" class="auth-toast-container">
    @foreach (['success', 'error', 'info', 'toast_error', 'toast_success'] as $type)
        @if (session($type))
            @php
                $toastClass = [
                    'success' => 'auth-toast-success',
                    'error' => 'auth-toast-error',
                    'info' => 'auth-toast-info',
                    'toast_error' => 'auth-toast-error',
                    'toast_success' => 'auth-toast-success',
                ][$type];

                $iconType = in_array($type, ['success', 'toast_success']) ? 'success' :
                           (in_array($type, ['error', 'toast_error']) ? 'error' : 'info');
            @endphp

            <div class="auth-toast {{ $toastClass }}" data-type="{{ $type }}">
                <!-- Icon -->
                <div class="auth-toast-icon">
                    @if($iconType === 'success')
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    @elseif($iconType === 'error')
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z" />
                        </svg>
                    @else
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    @endif
                </div>

                <!-- Message -->
                <div class="auth-toast-message">
                    {{ session($type) }}
                </div>

                <!-- Close Button -->
                <button class="auth-toast-close" onclick="closeAuthToast(this)" aria-label="Close notification">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
        @endif
    @endforeach
</div>

<script>
// Auth Toast functionality
function closeAuthToast(button) {
    const toast = button.closest('.auth-toast');
    toast.classList.add('hide');
    setTimeout(() => {
        toast.remove();
    }, 400);
}

// Show auth toasts on page load
document.addEventListener('DOMContentLoaded', function() {
    const toasts = document.querySelectorAll('.auth-toast');

    toasts.forEach((toast, index) => {
        // Stagger the appearance of multiple toasts
        setTimeout(() => {
            toast.classList.add('show');

            // Auto-hide after 5 seconds
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.classList.add('hide');
                    setTimeout(() => {
                        if (toast.parentNode) {
                            toast.remove();
                        }
                    }, 400);
                }
            }, 5000);
        }, index * 200); // 200ms delay between each toast
    });
});

// Function to create dynamic auth toasts (for JavaScript usage)
function showAuthToast(message, type = 'info') {
    const container = document.getElementById('auth-toast-container');
    if (!container) return;

    const icons = {
        success: `<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>`,
        error: `<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                 <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z" />
               </svg>`,
        info: `<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>`
    };

    const toast = document.createElement('div');
    toast.className = `auth-toast auth-toast-${type}`;
    toast.innerHTML = `
        <div class="auth-toast-icon">${icons[type] || icons.info}</div>
        <div class="auth-toast-message">${message}</div>
        <button class="auth-toast-close" onclick="closeAuthToast(this)" aria-label="Close notification">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
            </svg>
        </button>
    `;

    container.appendChild(toast);

    // Show toast with smooth animation
    requestAnimationFrame(() => {
        setTimeout(() => {
            toast.classList.add('show');
        }, 50);
    });

    // Auto-hide after 6 seconds (increased for better UX)
    setTimeout(() => {
        if (toast.parentNode) {
            toast.classList.add('hide');
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.remove();
                }
            }, 300);
        }
    }, 6000);
}
</script>
