<style>
    .auth-toast-container {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        max-width: 400px;
        width: 100%;
    }

    .auth-toast {
        display: flex;
        align-items: flex-start;
        padding: 16px;
        margin-bottom: 12px;
        background: rgba(0, 0, 0, 0.9);
        color: white;
        border-radius: 12px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
        border-left: 4px solid;
        backdrop-filter: blur(10px);
        opacity: 0;
        transform: translateX(100%) scale(0.95);
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
    }

    .auth-toast.show {
        opacity: 1;
        transform: translateX(0) scale(1);
    }

    .auth-toast.hide {
        opacity: 0;
        transform: translateX(100%) scale(0.95);
    }

    .auth-toast-success {
        border-left-color: #10b981;
    }

    .auth-toast-error {
        border-left-color: #ef4444;
    }

    .auth-toast-info {
        border-left-color: #3b82f6;
    }

    .auth-toast-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        margin-right: 12px;
        font-size: 18px;
        border-radius: 6px;
        flex-shrink: 0;
    }

    .auth-toast-message {
        flex: 1;
        font-size: 14px;
        line-height: 1.5;
        font-weight: 500;
    }

    .auth-toast-close {
        background: none;
        border: none;
        color: rgba(255, 255, 255, 0.7);
        cursor: pointer;
        padding: 4px;
        border-radius: 4px;
        transition: all 0.2s ease;
        margin-left: 8px;
        flex-shrink: 0;
    }

    .auth-toast-close:hover {
        color: white;
        background: rgba(255, 255, 255, 0.1);
    }

    .auth-toast-close svg {
        width: 18px;
        height: 18px;
    }

    @media (max-width: 768px) {
        .auth-toast-container {
            top: 10px;
            right: 10px;
            left: 10px;
            max-width: none;
        }

        .auth-toast {
            padding: 12px;
            font-size: 13px;
        }

        .auth-toast-icon {
            width: 28px;
            height: 28px;
            font-size: 16px;
        }
    }

</style>

<!-- 🌟 Auth Toast Container -->
<div id="auth-toast-container" class="auth-toast-container">
    @foreach (['success' => '✅', 'error' => '❌', 'info' => 'ℹ️', 'toast_error' => '❌', 'toast_success' => '✅'] as $type => $icon)
        @if (session($type))
            @php
                $toastClass = [
                    'success' => 'auth-toast-success',
                    'error' => 'auth-toast-error',
                    'info' => 'auth-toast-info',
                    'toast_error' => 'auth-toast-error',
                    'toast_success' => 'auth-toast-success',
                ][$type];
            @endphp

            <div class="auth-toast {{ $toastClass }}" data-type="{{ $type }}">
                <!-- Icon -->
                <div class="auth-toast-icon">
                    {{ $icon }}
                </div>

                <!-- Message -->
                <div class="auth-toast-message">
                    {{ session($type) }}
                </div>

                <!-- Close Button -->
                <button class="auth-toast-close" onclick="closeAuthToast(this)">
                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor" stroke-width="2">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
        @endif
    @endforeach
</div>

<script>
// Auth Toast functionality
function closeAuthToast(button) {
    const toast = button.closest('.auth-toast');
    toast.classList.add('hide');
    setTimeout(() => {
        toast.remove();
    }, 400);
}

// Show auth toasts on page load
document.addEventListener('DOMContentLoaded', function() {
    const toasts = document.querySelectorAll('.auth-toast');

    toasts.forEach((toast, index) => {
        // Stagger the appearance of multiple toasts
        setTimeout(() => {
            toast.classList.add('show');

            // Auto-hide after 5 seconds
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.classList.add('hide');
                    setTimeout(() => {
                        if (toast.parentNode) {
                            toast.remove();
                        }
                    }, 400);
                }
            }, 5000);
        }, index * 200); // 200ms delay between each toast
    });
});

// Function to create dynamic auth toasts (for JavaScript usage)
function showAuthToast(message, type = 'info') {
    const container = document.getElementById('auth-toast-container');
    if (!container) return;

    const icons = {
        success: '✅',
        error: '❌',
        info: 'ℹ️'
    };

    const toast = document.createElement('div');
    toast.className = `auth-toast auth-toast-${type}`;
    toast.innerHTML = `
        <div class="auth-toast-icon">${icons[type] || icons.info}</div>
        <div class="auth-toast-message">${message}</div>
        <button class="auth-toast-close" onclick="closeAuthToast(this)">
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="none" viewBox="0 0 24 24"
                stroke="currentColor" stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
            </svg>
        </button>
    `;

    container.appendChild(toast);

    // Show toast
    setTimeout(() => {
        toast.classList.add('show');
    }, 100);

    // Auto-hide after 5 seconds
    setTimeout(() => {
        if (toast.parentNode) {
            toast.classList.add('hide');
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.remove();
                }
            }, 400);
        }
    }, 5000);
}
</script>
