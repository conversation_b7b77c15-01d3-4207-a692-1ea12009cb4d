<style>
    .auth-toast-container {
        position: fixed;
        top: 32px;
        right: 32px;
        z-index: 10000;
        max-width: 380px;
        width: 100%;
        pointer-events: none;
    }

    .auth-toast {
        display: flex;
        align-items: flex-start;
        padding: 20px 24px;
        margin-bottom: 20px;
        background: #ffffff;
        color: #1f2937;
        border-radius: 20px;
        box-shadow:
            0 25px 50px -12px rgba(0, 0, 0, 0.25),
            0 0 0 1px rgba(0, 0, 0, 0.05);
        border: none;
        opacity: 0;
        transform: translateX(120%) rotateY(15deg);
        transition: all 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
        position: relative;
        pointer-events: auto;
        overflow: hidden;
    }

    .auth-toast::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
        pointer-events: none;
    }

    .auth-toast.show {
        opacity: 1;
        transform: translateX(0) rotateY(0deg);
    }

    .auth-toast.hide {
        opacity: 0;
        transform: translateX(120%) rotateY(-15deg) scale(0.8);
        transition: all 0.4s cubic-bezier(0.55, 0.085, 0.68, 0.53);
    }

    .auth-toast-success {
        background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
        color: #166534;
        box-shadow:
            0 25px 50px -12px rgba(34, 197, 94, 0.25),
            0 0 0 1px rgba(34, 197, 94, 0.1);
    }

    .auth-toast-error {
        background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
        color: #991b1b;
        box-shadow:
            0 25px 50px -12px rgba(239, 68, 68, 0.25),
            0 0 0 1px rgba(239, 68, 68, 0.1);
    }

    .auth-toast-info {
        background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
        color: #1e40af;
        box-shadow:
            0 25px 50px -12px rgba(59, 130, 246, 0.25),
            0 0 0 1px rgba(59, 130, 246, 0.1);
    }

    .auth-toast-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 48px;
        height: 48px;
        margin-right: 18px;
        margin-top: 2px;
        border-radius: 50%;
        flex-shrink: 0;
        position: relative;
        transition: transform 0.3s ease;
    }

    .auth-toast:hover .auth-toast-icon {
        transform: scale(1.1) rotate(5deg);
    }

    .auth-toast-success .auth-toast-icon {
        background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
        color: white;
        box-shadow: 0 8px 16px rgba(34, 197, 94, 0.3);
    }

    .auth-toast-error .auth-toast-icon {
        background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        color: white;
        box-shadow: 0 8px 16px rgba(239, 68, 68, 0.3);
    }

    .auth-toast-info .auth-toast-icon {
        background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
        color: white;
        box-shadow: 0 8px 16px rgba(59, 130, 246, 0.3);
    }

    .auth-toast-icon svg {
        width: 24px;
        height: 24px;
        stroke-width: 2;
        filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
    }

    .auth-toast-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 4px;
    }

    .auth-toast-title {
        font-size: 16px;
        font-weight: 600;
        line-height: 1.3;
        margin: 0;
    }

    .auth-toast-message {
        font-size: 14px;
        line-height: 1.4;
        font-weight: 400;
        opacity: 0.8;
        margin: 0;
    }

    .auth-toast-close {
        background: rgba(0, 0, 0, 0.05);
        border: none;
        color: currentColor;
        cursor: pointer;
        padding: 10px;
        border-radius: 50%;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        margin-left: 16px;
        margin-top: 2px;
        flex-shrink: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0.6;
        width: 36px;
        height: 36px;
    }

    .auth-toast-close:hover {
        opacity: 1;
        background: rgba(0, 0, 0, 0.1);
        transform: scale(1.1);
    }

    .auth-toast-close svg {
        width: 18px;
        height: 18px;
        stroke-width: 2.5;
    }

    @media (max-width: 768px) {
        .auth-toast-container {
            top: 20px;
            right: 20px;
            left: 20px;
            max-width: none;
        }

        .auth-toast {
            padding: 18px 20px;
            margin-bottom: 16px;
            border-radius: 16px;
        }

        .auth-toast-icon {
            width: 42px;
            height: 42px;
            margin-right: 16px;
        }

        .auth-toast-icon svg {
            width: 20px;
            height: 20px;
        }

        .auth-toast-title {
            font-size: 15px;
        }

        .auth-toast-message {
            font-size: 13px;
        }

        .auth-toast-close {
            width: 32px;
            height: 32px;
            padding: 8px;
        }

        .auth-toast-close svg {
            width: 16px;
            height: 16px;
        }
    }

    @media (prefers-reduced-motion: reduce) {
        .auth-toast {
            transition: opacity 0.3s ease;
            transform: none;
        }

        .auth-toast.show {
            transform: none;
        }

        .auth-toast.hide {
            transform: none;
        }

        .auth-toast:hover .auth-toast-icon {
            transform: none;
        }
    }

</style>

<!-- Elegant Auth Toast Container -->
<div id="auth-toast-container" class="auth-toast-container">
    @foreach (['success', 'error', 'info', 'toast_error', 'toast_success'] as $type)
        @if (session($type))
            @php
                $toastClass = [
                    'success' => 'auth-toast-success',
                    'error' => 'auth-toast-error',
                    'info' => 'auth-toast-info',
                    'toast_error' => 'auth-toast-error',
                    'toast_success' => 'auth-toast-success',
                ][$type];

                $iconType = in_array($type, ['success', 'toast_success']) ? 'success' :
                           (in_array($type, ['error', 'toast_error']) ? 'error' : 'info');

                $titles = [
                    'success' => 'Success!',
                    'error' => 'Error!',
                    'info' => 'Information'
                ];
            @endphp

            <div class="auth-toast {{ $toastClass }}" data-type="{{ $type }}">
                <!-- Icon -->
                <div class="auth-toast-icon">
                    @if($iconType === 'success')
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    @elseif($iconType === 'error')
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z" />
                        </svg>
                    @else
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    @endif
                </div>

                <!-- Content -->
                <div class="auth-toast-content">
                    <h4 class="auth-toast-title">{{ $titles[$iconType] }}</h4>
                    <p class="auth-toast-message">{{ session($type) }}</p>
                </div>

                <!-- Close Button -->
                <button class="auth-toast-close" onclick="closeAuthToast(this)" aria-label="Close notification">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
        @endif
    @endforeach
</div>

<script>
// Auth Toast functionality
function closeAuthToast(button) {
    const toast = button.closest('.auth-toast');
    toast.classList.add('hide');
    setTimeout(() => {
        toast.remove();
    }, 400);
}

// Show auth toasts on page load
document.addEventListener('DOMContentLoaded', function() {
    const toasts = document.querySelectorAll('.auth-toast');

    toasts.forEach((toast, index) => {
        // Stagger the appearance of multiple toasts
        setTimeout(() => {
            toast.classList.add('show');

            // Auto-hide after 5 seconds
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.classList.add('hide');
                    setTimeout(() => {
                        if (toast.parentNode) {
                            toast.remove();
                        }
                    }, 400);
                }
            }, 5000);
        }, index * 200); // 200ms delay between each toast
    });
});

// Function to create dynamic auth toasts (for JavaScript usage)
function showAuthToast(message, type = 'info', title = null) {
    const container = document.getElementById('auth-toast-container');
    if (!container) return;

    const config = {
        success: {
            icon: `<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                     <path stroke-linecap="round" stroke-linejoin="round" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                   </svg>`,
            title: 'Success!'
        },
        error: {
            icon: `<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                     <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z" />
                   </svg>`,
            title: 'Error!'
        },
        info: {
            icon: `<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                     <path stroke-linecap="round" stroke-linejoin="round" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                   </svg>`,
            title: 'Information'
        }
    };

    const toastConfig = config[type] || config.info;
    const toastTitle = title || toastConfig.title;

    const toast = document.createElement('div');
    toast.className = `auth-toast auth-toast-${type}`;
    toast.innerHTML = `
        <div class="auth-toast-icon">${toastConfig.icon}</div>
        <div class="auth-toast-content">
            <h4 class="auth-toast-title">${toastTitle}</h4>
            <p class="auth-toast-message">${message}</p>
        </div>
        <button class="auth-toast-close" onclick="closeAuthToast(this)" aria-label="Close notification">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
            </svg>
        </button>
    `;

    container.appendChild(toast);

    // Show toast with smooth animation
    requestAnimationFrame(() => {
        setTimeout(() => {
            toast.classList.add('show');
        }, 100);
    });

    // Auto-hide after 7 seconds
    setTimeout(() => {
        if (toast.parentNode) {
            toast.classList.add('hide');
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.remove();
                }
            }, 400);
        }
    }, 7000);
}
</script>
